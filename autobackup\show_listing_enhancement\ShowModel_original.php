    public function getShows($status = null, $limit = null, $offset = null, $excludeCompletedCancelled = false) {
        try {
            // Check if tables exist
            $usersTableExists = $this->tableExists('users');
            $registrationsTableExists = $this->tableExists('registrations');
            
            // Build SQL based on available tables
            if ($usersTableExists && $registrationsTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id';
            } else if ($usersTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id';
                error_log("Registrations table not found, using simplified query for getShows");
            } else if ($registrationsTableExists) {
                $sql = 'SELECT s.*, <PERSON>ULL as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s';
                error_log("Users table not found, using simplified query for getShows");
            } else {
                $sql = 'SELECT s.*, NULL as coordinator_name, 0 as registration_count 
                        FROM shows s';
                error_log("Users and registrations tables not found, using simplified query for getShows");
            }
            
            $whereConditions = [];
            
            if ($status) {
                $whereConditions[] = 's.status = :status';
            }
            
            if ($excludeCompletedCancelled) {
                $whereConditions[] = 's.status NOT IN ("completed", "cancelled")';
            }
            
            if (!empty($whereConditions)) {
                $sql .= ' WHERE ' . implode(' AND ', $whereConditions);
            }
            
            $sql .= ' ORDER BY s.start_date DESC';
            
            if ($limit) {
                $sql .= ' LIMIT :limit';
                if ($offset) {
                    $sql .= ' OFFSET :offset';
                }
            }
            
            $this->db->query($sql);
            
            if ($status) {
                $this->db->bind(':status', $status);
            }
            
            if ($limit) {
                $this->db->bind(':limit', $limit, PDO::PARAM_INT);
                if ($offset) {
                    $this->db->bind(':offset', $offset, PDO::PARAM_INT);
                }
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShows: " . $e->getMessage());
            return [];
        }
    }