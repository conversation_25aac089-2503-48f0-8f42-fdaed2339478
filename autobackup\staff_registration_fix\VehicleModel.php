<?php
/**
 * Vehicle Model - BACKUP BEFORE FIX
 * 
 * This model handles all database operations related to vehicles.
 */
class VehicleModel {
    private $db;
    
    /**
     * Update vehicle primary image
     * 
     * @param int $vehicleId Vehicle ID
     * @param string $imageName Image filename
     * @return bool
     */
    public function updateVehiclePrimaryImage($vehicleId, $imageName) {
        try {
            // Start transaction for data consistency
            $this->db->beginTransaction();
            
            // First, reset all images for this vehicle to not be primary
            $this->db->query('UPDATE images SET is_primary = 0 WHERE entity_type = "vehicle" AND entity_id = :vehicle_id');
            $this->db->bind(':vehicle_id', $vehicleId);
            $this->db->execute();
            
            // Then, set the specified image as primary
            $this->db->query('UPDATE images SET is_primary = 1 WHERE entity_type = "vehicle" AND entity_id = :vehicle_id AND file_name = :file_name');
            $this->db->bind(':vehicle_id', $vehicleId);
            $this->db->bind(':file_name', $imageName);
            $this->db->execute();
            
            // Commit the transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback the transaction on error
            $this->db->rollBack();
            
            // Log error if needed
            error_log('Error in updateVehiclePrimaryImage: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Load a model
     * 
     * @param string $model Model name
     * @return object Model instance
     */
    private function loadModel($model) {
        // Require model file
        require_once dirname(dirname(__FILE__)) . '/models/' . $model . '.php';
        
        // Instantiate model
        return new $model();
    }
    
    /**
     * Get all vehicles for a user
     * 
     * @param int $ownerId User ID
     * @return array Array of vehicle objects
     */
    public function getUserVehicles($ownerId) {
        try {
            // First get all vehicles
            $this->db->query('SELECT v.*, u.name as owner_name 
                            FROM vehicles v 
                            JOIN users u ON v.owner_id = u.id
                            WHERE v.owner_id = :owner_id 
                            ORDER BY v.year DESC, v.make, v.model');
            $this->db->bind(':owner_id', $ownerId);
            
            $vehicles = $this->db->resultSet();
            
            if (!is_array($vehicles) || empty($vehicles)) {
                return [];
            }
            
            // Get primary images for each vehicle
            // Use the Controller's model method to load the ImageEditorModel
            $imageEditorModel = $this->loadModel('ImageEditorModel');
            
            foreach ($vehicles as $vehicle) {
                // Try to get primary image from the new images table
                $images = $imageEditorModel->getImagesByEntity('vehicle', $vehicle->id);
                
                $primaryImage = null;
                if (!empty($images)) {
                    foreach ($images as $image) {
                        if (isset($image->is_primary) && $image->is_primary) {
                            $primaryImage = $image;
                            break;
                        }
                    }
                    
                    // If no primary image is set, use the first one
                    if (!$primaryImage && count($images) > 0) {
                        $primaryImage = $images[0];
                    }
                    
                    if ($primaryImage) {
                        $vehicle->primary_image = isset($primaryImage->file_name) ? $primaryImage->file_name : '';
                    }
                }
            }
            
            return $vehicles;
        } catch (Exception $e) {
            // Log error if needed
            error_log('Error in getUserVehicles: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get vehicle by ID
     * 
     * @param int $id Vehicle ID
     * @return object|bool Vehicle object or false if not found
     */
    public function getVehicleById($id) {
        try {
            $this->db->query('SELECT v.*, u.name as owner_name 
                            FROM vehicles v 
                            JOIN users u ON v.owner_id = u.id
                            WHERE v.id = :id');
            $this->db->bind(':id', $id);
            
            $vehicle = $this->db->single();
            
            if (!$vehicle) {
                return false;
            }
            
            // Get primary image for this vehicle
            $imageEditorModel = $this->loadModel('ImageEditorModel');
            $images = $imageEditorModel->getImagesByEntity('vehicle', $vehicle->id);
            
            $primaryImage = null;
            if (!empty($images)) {
                foreach ($images as $image) {
                    if (isset($image->is_primary) && $image->is_primary) {
                        $primaryImage = $image;
                        break;
                    }
                }
                
                // If no primary image is set, use the first one
                if (!$primaryImage && count($images) > 0) {
                    $primaryImage = $images[0];
                }
                
                if ($primaryImage) {
                    $vehicle->primary_image = isset($primaryImage->file_name) ? $primaryImage->file_name : '';
                }
            }
            
            return $vehicle;
        } catch (Exception $e) {
            // Log error if needed
            error_log('Error in getVehicleById: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Add a new vehicle
     * 
     * @param array $data Vehicle data
     * @return int|bool New vehicle ID or false if failed
     */
    public function addVehicle($data) {
        try {
            // Start transaction for data consistency
            $this->db->beginTransaction();
            
            // Insert vehicle
            $this->db->query('INSERT INTO vehicles (owner_id, make, model, year, color, license_plate, vin, notes, created_at, updated_at) 
                            VALUES (:owner_id, :make, :model, :year, :color, :license_plate, :vin, :notes, NOW(), NOW())');
            
            // Bind values
            $this->db->bind(':owner_id', $data['owner_id']);
            $this->db->bind(':make', $data['make']);
            $this->db->bind(':model', $data['model']);
            $this->db->bind(':year', $data['year']);
            $this->db->bind(':color', $data['color']);
            $this->db->bind(':license_plate', $data['license_plate'] ?? '');
            $this->db->bind(':vin', $data['vin'] ?? '');
            $this->db->bind(':notes', $data['notes'] ?? '');
            
            // Execute
            $result = $this->db->execute();
            
            if (!$result) {
                $this->db->rollBack();
                return false;
            }
            
            // Get the new vehicle ID
            $vehicleId = $this->db->lastInsertId();
            
            // Commit the transaction
            $this->db->commit();
            
            return $vehicleId;
        } catch (Exception $e) {
            // Rollback the transaction on error
            $this->db->rollBack();
            
            // Log error if needed
            error_log('Error in addVehicle: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update a vehicle
     * 
     * @param array $data Vehicle data
     * @return bool
     */
    public function updateVehicle($data) {
        try {
            // Start transaction for data consistency
            $this->db->beginTransaction();
            
            // Update vehicle
            $this->db->query('UPDATE vehicles 
                            SET make = :make, 
                                model = :model, 
                                year = :year, 
                                color = :color, 
                                license_plate = :license_plate, 
                                vin = :vin, 
                                notes = :notes, 
                                updated_at = NOW() 
                            WHERE id = :id');
            
            // Bind values
            $this->db->bind(':id', $data['id']);
            $this->db->bind(':make', $data['make']);
            $this->db->bind(':model', $data['model']);
            $this->db->bind(':year', $data['year']);
            $this->db->bind(':color', $data['color']);
            $this->db->bind(':license_plate', $data['license_plate'] ?? '');
            $this->db->bind(':vin', $data['vin'] ?? '');
            $this->db->bind(':notes', $data['notes'] ?? '');
            
            // Execute
            $result = $this->db->execute();
            
            if (!$result) {
                $this->db->rollBack();
                return false;
            }
            
            // Commit the transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback the transaction on error
            $this->db->rollBack();
            
            // Log error if needed
            error_log('Error in updateVehicle: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete a vehicle
     * 
     * @param int $id Vehicle ID
     * @return bool
     */
    public function deleteVehicle($id) {
        try {
            // Start transaction for data consistency
            $this->db->beginTransaction();
            
            // Delete vehicle
            $this->db->query('DELETE FROM vehicles WHERE id = :id');
            $this->db->bind(':id', $id);
            
            // Execute
            $result = $this->db->execute();
            
            if (!$result) {
                $this->db->rollBack();
                return false;
            }
            
            // Commit the transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback the transaction on error
            $this->db->rollBack();
            
            // Log error if needed
            error_log('Error in deleteVehicle: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all vehicles
     * 
     * @return array Array of vehicle objects
     */
    public function getAllVehicles() {
        try {
            $this->db->query('SELECT v.*, u.name as owner_name 
                            FROM vehicles v 
                            JOIN users u ON v.owner_id = u.id
                            ORDER BY v.year DESC, v.make, v.model');
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            // Log error if needed
            error_log('Error in getAllVehicles: ' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Search vehicles
     * 
     * @param string $searchTerm Search term
     * @return array Array of vehicle objects
     */
    public function searchVehicles($searchTerm) {
        try {
            $this->db->query('SELECT v.*, u.name as owner_name 
                            FROM vehicles v 
                            JOIN users u ON v.owner_id = u.id
                            WHERE v.make LIKE :search 
                            OR v.model LIKE :search 
                            OR v.year LIKE :search 
                            OR v.color LIKE :search 
                            OR v.license_plate LIKE :search 
                            OR v.vin LIKE :search 
                            OR u.name LIKE :search
                            ORDER BY v.year DESC, v.make, v.model');
            
            $searchTerm = '%' . $searchTerm . '%';
            $this->db->bind(':search', $searchTerm);
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            // Log error if needed
            error_log('Error in searchVehicles: ' . $e->getMessage());
            return [];
        }
    }
}