<?php
/**
 * User Model
 * 
 * This model handles all database operations related to users.
 */
class UserModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all users
     * 
     * @param string $role Optional role filter
     * @return array
     */
    public function getUsers($role = null) {
        $sql = 'SELECT id, name, email, role, profile_image, phone, address, city, state, zip, created_at, last_login, status FROM users';
        
        if ($role) {
            $sql .= ' WHERE role = :role';
            $this->db->query($sql);
            $this->db->bind(':role', $role);
        } else {
            $this->db->query($sql);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Get user by ID
     * 
     * @param int $id User ID
     * @return object|bool User object or false if not found
     */
    public function getUserById($id) {
        $this->db->query('SELECT id, name, email, role, profile_image, phone, address, city, state, zip, created_at, last_login, status 
                          FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Get user by email
     * 
     * @param string $email User email
     * @return object|bool User object or false if not found
     */
    public function getUserByEmail($email) {
        $this->db->query('SELECT id, name, email, password, role, profile_image, phone, address, city, state, zip, created_at, last_login, status 
                          FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        
        return $this->db->single();
    }
    
    /**
     * Create a new user
     * 
     * @param array $data User data
     * @return bool|int False on failure, user ID on success
     */
    public function createUser($data) {
        $this->db->query('INSERT INTO users (name, email, password, role, phone, address, city, state, zip, created_at) 
                          VALUES (:name, :email, :password, :role, :phone, :address, :city, :state, :zip, NOW())');
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':password', password_hash($data['password'], PASSWORD_BCRYPT, ['cost' => 12]));
        $this->db->bind(':role', $data['role']);
        $this->db->bind(':phone', $data['phone'] ?? null);
        $this->db->bind(':address', $data['address'] ?? null);
        $this->db->bind(':city', $data['city'] ?? null);
        $this->db->bind(':state', $data['state'] ?? null);
        $this->db->bind(':zip', $data['zip'] ?? null);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update a user
     * 
     * @param array $data User data
     * @return bool
     */
    public function updateUser($data) {
        // If password is provided, update it too
        if (!empty($data['password'])) {
            $this->db->query('UPDATE users SET name = :name, email = :email, password = :password, 
                              role = :role, status = :status, phone = :phone, address = :address, 
                              city = :city, state = :state, zip = :zip WHERE id = :id');
            $this->db->bind(':password', password_hash($data['password'], PASSWORD_BCRYPT, ['cost' => 12]));
        } else {
            $this->db->query('UPDATE users SET name = :name, email = :email, 
                              role = :role, status = :status, phone = :phone, address = :address, 
                              city = :city, state = :state, zip = :zip WHERE id = :id');
        }
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':email', $data['email']);
        $this->db->bind(':role', $data['role']);
        $this->db->bind(':status', $data['status']);
        $this->db->bind(':phone', $data['phone'] ?? null);
        $this->db->bind(':address', $data['address'] ?? null);
        $this->db->bind(':city', $data['city'] ?? null);
        $this->db->bind(':state', $data['state'] ?? null);
        $this->db->bind(':zip', $data['zip'] ?? null);
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
    
    /**
     * Update user profile
     * 
     * @param array $data Profile data
     * @return bool
     */
    public function updateProfile($data) {
        // If password is provided, update it too
        if (!empty($data['password'])) {
            $this->db->query('UPDATE users SET name = :name, phone = :phone, address = :address, 
                              city = :city, state = :state, zip = :zip, password = :password 
                              WHERE id = :id');
            $this->db->bind(':password', password_hash($data['password'], PASSWORD_BCRYPT, ['cost' => 12]));
        } else {
            $this->db->query('UPDATE users SET name = :name, phone = :phone, address = :address, 
                              city = :city, state = :state, zip = :zip 
                              WHERE id = :id');
        }
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':phone', $data['phone'] ?? null);
        $this->db->bind(':address', $data['address'] ?? null);
        $this->db->bind(':city', $data['city'] ?? null);
        $this->db->bind(':state', $data['state'] ?? null);
        $this->db->bind(':zip', $data['zip'] ?? null);
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
    
    /**
     * Update user profile image
     * 
     * @param int $id User ID
     * @param string $imagePath Path to profile image
     * @return bool
     */
    public function updateProfileImage($id, $imagePath) {
        $this->db->query('UPDATE users SET profile_image = :profile_image WHERE id = :id');
        $this->db->bind(':profile_image', $imagePath);
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Delete a user
     * 
     * @param int $id User ID
     * @return bool
     */
    public function deleteUser($id) {
        $this->db->query('DELETE FROM users WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Get judges
     * 
     * @return array
     */
    public function getJudges() {
        $this->db->query('SELECT id, name, email, profile_image FROM users WHERE role = :role AND status = :status');
        $this->db->bind(':role', 'judge');
        $this->db->bind(':status', 'active');
        
        return $this->db->resultSet();
    }
    
    /**
     * Check if email exists
     * 
     * @param string $email Email to check
     * @param int $excludeId Optional user ID to exclude from check
     * @return bool
     */
    public function emailExists($email, $excludeId = null) {
        if ($excludeId) {
            $this->db->query('SELECT id FROM users WHERE email = :email AND id != :id');
            $this->db->bind(':id', $excludeId);
        } else {
            $this->db->query('SELECT id FROM users WHERE email = :email');
        }
        
        $this->db->bind(':email', $email);
        $this->db->single();
        
        return $this->db->rowCount() > 0;
    }
    
    /**
     * Get users by role
     * 
     * @param string $role Role to filter by
     * @return array Array of users with the specified role
     */
    public function getUsersByRole($role) {
        // Get users with the specified role
        $this->db->query('SELECT id, name, email, role, profile_image, phone, address, city, state, zip, created_at, last_login, status 
                          FROM users WHERE role = :role ORDER BY name');
        $this->db->bind(':role', $role);
        
        return $this->db->resultSet();
    }
    
    /**
     * Check if a user has a specific role
     * 
     * @param int $userId User ID
     * @param string $role Role name
     * @return bool True if the user has the role, false otherwise
     */
    public function hasRole($userId, $role) {
        // Check if we're using the new role system
        $this->db->query("SHOW TABLES LIKE 'roles'");
        $hasRolesTable = $this->db->rowCount() > 0;
        
        if ($hasRolesTable) {
            // New role system with roles table
            $this->db->query('SELECT COUNT(*) as count 
                              FROM user_roles ur 
                              JOIN roles r ON ur.role_id = r.id 
                              WHERE ur.user_id = :user_id AND r.name = :role');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role', $role);
            
            $result = $this->db->single();
            
            return $result && $result->count > 0;
        } else {
            // Old role system with role column in users table
            $this->db->query('SELECT COUNT(*) as count FROM users WHERE id = :user_id AND role = :role');
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':role', $role);
            
            $result = $this->db->single();
            
            return $result && $result->count > 0;
        }
    }
    
    /**
     * Get users by multiple roles
     * 
     * @param array $roles Array of roles to filter by
     * @return array Array of users with any of the specified roles
     */
    public function getUsersByRoles($roles) {
        if (empty($roles)) {
            return [];
        }
        
        // Create named placeholders for the IN clause
        $placeholders = [];
        $params = [];
        
        foreach ($roles as $index => $role) {
            $param = ":role{$index}";
            $placeholders[] = $param;
            $params[$param] = $role;
        }
        
        $placeholderString = implode(',', $placeholders);
        
        $this->db->query("SELECT id, name, email, role, profile_image, created_at, last_login, status 
                          FROM users WHERE role IN ($placeholderString) ORDER BY name");
        
        // Bind each role value
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Get count of users by role
     * 
     * @return array Array of role counts
     */
    public function getRoleCounts() {
        $this->db->query('SELECT role, COUNT(*) as count FROM users GROUP BY role');
        $results = $this->db->resultSet();
        
        $counts = [];
        foreach ($results as $result) {
            $counts[$result->role] = $result->count;
        }
        
        return $counts;
    }
    
    /**
     * Update user role
     * 
     * @param int $userId User ID
     * @param string $role New role
     * @return bool True on success, false on failure
     */
    public function updateUserRole($userId, $role) {
        $this->db->query('UPDATE users SET role = :role WHERE id = :id');
        $this->db->bind(':role', $role);
        $this->db->bind(':id', $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Get all users except the specified user
     * 
     * @param int $excludeUserId User ID to exclude
     * @return array Array of users
     */
    public function getUsersExcept($excludeUserId) {
        $this->db->query('SELECT id, name, email, role, profile_image, created_at, last_login, status 
                          FROM users WHERE id != :exclude_id ORDER BY role, name');
        $this->db->bind(':exclude_id', $excludeUserId);
        
        return $this->db->resultSet();
    }
}