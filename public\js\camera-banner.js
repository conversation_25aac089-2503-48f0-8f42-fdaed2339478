/**
 * Camera Banner Advertisement System
 * Handles rotating banners for camera and QR scanner modals
 */

// Ensure BASE_URL is defined
if (typeof BASE_URL === 'undefined') {
    window.BASE_URL = window.location.origin;
}

class CameraBanner {
    constructor() {
        // Initialize properties first - no try/catch to avoid masking errors
        this.version = '3.64.0-cache-bust-fix';
        this.banners = [];
        this.otherBanners = []; // Non-logo banners for randomization
        this.currentIndex = 0;
        this.rotationInterval = null;
        this.defaultDelay = 5000; // 5 seconds default
        this.debugEnabled = false;
        this.isFirstShow = true; // Track if we need to show logo first
        this.siteLogo = '/uploads/branding/logo_1751468505_rides_logo.png'; // Default fallback
        
        // Debug constructor execution
        if (typeof window !== 'undefined' && window.console) {
            console.log('CameraBanner constructor completed, version:', this.version);
        }
        
        // Initialize async after constructor completes
        setTimeout(() => {
            this.init().catch(error => {
                console.error('CameraBanner async init failed:', error);
            });
        }, 0);
    }

    async init() {
        console.log('CameraBanner: Init method called, version:', this.version);
        
        try {
            // Check debug mode first
            this.checkDebugMode();
            
            if (this.isDebugMode()) {
                console.log('CameraBanner: Debug mode enabled');
                console.log('CameraBanner: BASE_URL =', BASE_URL);
            }
            
            console.log('CameraBanner: About to load site logo');
            await this.loadSiteLogo();
            
            console.log('CameraBanner: About to load banners');
            await this.loadBanners();
            
            console.log('CameraBanner: Initialization complete, banners loaded:', this.banners.length);
            
        } catch (error) {
            console.error('CameraBanner init error:', error);
            
            // Ensure we have at least fallback data
            if (!this.banners || this.banners.length === 0) {
                this.banners = [
                    {
                        id: -1,
                        type: 'text',
                        text: 'Rowan Elite Rides',
                        image_path: '',
                        alt_text: 'Rowan Elite Rides Logo',
                        active: true,
                        sort_order: -1,
                        is_logo: true
                    }
                ];
                this.separateBanners();
            }
        }
    }

    async loadSiteLogo() {
        try {
            const response = await fetch(BASE_URL + '/api/getSiteLogo');
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.logo_path) {
                    this.siteLogo = data.logo_path;
                    if (this.isDebugMode()) {
                        console.log('CameraBanner: Site logo loaded:', this.siteLogo);
                    }
                }
            }
        } catch (error) {
            if (this.isDebugMode()) {
                console.log('CameraBanner: Failed to load site logo, using fallback');
            }
        }
    }

    async loadBanners() {
        if (this.isDebugMode()) {
            console.log('CameraBanner: Starting to load banners from API...');
        }
        
        try {
            // Try to load from API first using proper routing
            const response = await fetch(BASE_URL + '/api/cameraBanners');
            
            if (this.isDebugMode()) {
                console.log('CameraBanner: API response status:', response.status);
            }
            
            if (response.ok) {
                const data = await response.json();
                
                if (this.isDebugMode()) {
                    console.log('CameraBanner: API response data:', data);
                    console.log('CameraBanner: API success:', data.success);
                    console.log('CameraBanner: API banners array:', data.banners);
                    console.log('CameraBanner: API banners is array:', Array.isArray(data.banners));
                }
                
                if (data.success && Array.isArray(data.banners)) {
                    this.banners = data.banners.filter(banner => banner && typeof banner === 'object');
                    this.defaultDelay = parseInt(data.delay) || 5000;
                    
                    if (this.isDebugMode()) {
                        console.log('CameraBanner: Loaded', this.banners.length, 'banners from API');
                        console.log('CameraBanner: First banner:', this.banners[0]);
                    }
                    
                    // Always ensure we have a logo banner
                    const hasLogo = this.banners.some(b => b.is_logo);
                    if (!hasLogo) {
                        this.banners.unshift({
                            id: -1,
                            type: 'text',
                            text: 'Rowan Elite Rides',
                            image_path: '',
                            alt_text: 'Rowan Elite Rides Logo',
                            active: true,
                            sort_order: -1,
                            is_logo: true
                        });
                    }
                } else {
                    throw new Error('Invalid API response');
                }
            } else {
                throw new Error('API not available');
            }
        } catch (error) {
            if (this.isDebugMode()) {
                console.error('CameraBanner: API failed, using fallback banners:', error);
                console.error('CameraBanner: Error details:', {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                });
            }
            
            // Always ensure we have at least the site logo
            this.banners = [
                {
                    id: -1,
                    type: 'text',
                    text: 'Rowan Elite Rides',
                    image_path: '',
                    alt_text: 'Rowan Elite Rides Logo',
                    active: true,
                    sort_order: -1,
                    is_logo: true
                }
            ];
            this.defaultDelay = 5000;
            
            // Re-throw the error so we can see it in the calling code
            throw error;
        }
        
        // Always separate banners after loading
        this.separateBanners();
    }

    separateBanners() {
        // Separate logo banner from other banners
        this.otherBanners = this.banners.filter(banner => !banner.is_logo);
    }

    shuffleArray(array) {
        // Fisher-Yates shuffle algorithm
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    checkDebugMode() {
        // Check if debug mode is enabled
        this.debugEnabled = document.body.dataset.debugMode === 'true' ||
                           window.location.search.includes('debug=1') ||
                           localStorage.getItem('camera_banner_debug') === 'true';
    }
    
    isDebugMode() {
        return this.debugEnabled;
    }



    startRotation(containerId) {
        if (this.banners.length === 0) {
            return;
        }

        const container = document.getElementById(containerId);
        if (!container) {
            return;
        }

        // Stop any existing rotation
        this.stopRotation();
        
        // FORCE clear all default content including nested divs
        container.innerHTML = '';
        // Also remove any child elements that might be lingering
        while (container.firstChild) {
            container.removeChild(container.firstChild);
        }
        
        // Reset for new rotation session
        this.isFirstShow = true;
        this.currentIndex = 0;



        // Always show logo first
        const logoBanner = this.banners.find(b => b.is_logo);

        if (logoBanner) {
            this.showLogoBanner(container, logoBanner);

            // Only start rotation if there are other banners
            if (this.otherBanners.length > 0) {
                // After 5 seconds, start rotating other banners
                setTimeout(() => {
                    this.startOtherBannersRotation(container);
                }, 5000);
            }
            // If no other banners, logo stays displayed permanently
        } else {
            this.startNormalRotation(container);
        }
    }

    startOtherBannersRotation(container) {
        if (this.otherBanners.length === 0) {
            return;
        }

        // Shuffle other banners for random order
        const shuffledBanners = this.shuffleArray(this.otherBanners);
        let otherIndex = 0;

        // Show first other banner immediately
        this.showBanner(container, shuffledBanners[otherIndex]);

        // Start rotation of other banners if we have more than one
        if (shuffledBanners.length > 1) {
            this.rotationInterval = setInterval(() => {
                otherIndex = (otherIndex + 1) % shuffledBanners.length;
                this.showBanner(container, shuffledBanners[otherIndex]);
            }, this.defaultDelay);
        }
    }

    startNormalRotation(container) {
        // Fallback for when no logo exists
        this.showBanner(container, this.banners[0]);

        if (this.banners.length > 1) {
            this.rotationInterval = setInterval(() => {
                this.currentIndex = (this.currentIndex + 1) % this.banners.length;
                this.showBanner(container, this.banners[this.currentIndex]);
            }, this.defaultDelay);
        }
    }

    stopRotation() {
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
            this.rotationInterval = null;
        }
        this.currentIndex = 0;

        if (this.isDebugMode()) {
            console.log('Stopped banner rotation');
        }
    }

    showLogoBanner(container, logoBanner) {
        if (this.isDebugMode()) {
            console.log('CameraBanner: showLogoBanner called', {
                container: container ? container.id : 'null',
                logoBanner: logoBanner
            });
        }
        
        if (!container || !logoBanner) {
            if (this.isDebugMode()) {
                console.error('CameraBanner: showLogoBanner - missing container or banner');
            }
            return;
        }

        // Force clear container completely
        container.innerHTML = '';
        // Remove any lingering child elements
        while (container.firstChild) {
            container.removeChild(container.firstChild);
        }

        if (logoBanner.image_path && logoBanner.image_path.trim() !== '') {
            if (this.isDebugMode()) {
                console.log('CameraBanner: Creating image banner with path:', logoBanner.image_path);
            }
            const img = document.createElement('img');
            img.src = logoBanner.image_path;
            img.alt = logoBanner.alt_text || 'Logo';
            img.className = 'logo-banner';
            img.style.cssText = `
                max-height: 100%;
                max-width: 100%;
                height: auto;
                width: auto;
                object-fit: contain;
                display: block;
                margin: 0 auto;
            `;
            img.onerror = () => {
                if (this.isDebugMode()) {
                    console.log('CameraBanner: Image failed to load, showing text instead');
                }
                this.showTextBanner(container, logoBanner.text || logoBanner.alt_text || 'Logo');
            };
            img.onload = () => {
                if (this.isDebugMode()) {
                    console.log('CameraBanner: Image loaded successfully');
                }
            };
            container.appendChild(img);
        } else {
            if (this.isDebugMode()) {
                console.log('CameraBanner: No image path, showing text banner:', logoBanner.text || logoBanner.alt_text || 'Logo');
            }
            // Use the text from the banner
            this.showTextBanner(container, logoBanner.text || logoBanner.alt_text || 'Logo');
        }

        // Force reflow to ensure visual update
        container.offsetHeight;
    }

    showBanner(container, bannerOrIndex) {
        if (!container || !document.contains(container)) {
            return;
        }

        // Handle both banner object and index
        let banner;
        if (typeof bannerOrIndex === 'object') {
            banner = bannerOrIndex;
        } else if (typeof bannerOrIndex === 'number' && this.banners && this.banners[bannerOrIndex]) {
            banner = this.banners[bannerOrIndex];
        } else {
            return;
        }

        // Skip inactive banners
        if (!banner || !banner.active) {
            this.currentIndex = (this.currentIndex + 1) % this.banners.length;
            this.showBanner(container, this.currentIndex);
            return;
        }

        // Smooth transition: fade out, change content, fade in
        container.style.transition = 'opacity 0.3s ease-in-out';
        container.style.opacity = '0';
        
        setTimeout(() => {
            // Clear container content
            container.innerHTML = '';
            
            // Display banner content
            if (banner.type === 'image' && banner.image_path && banner.image_path.trim() !== '') {
                const img = document.createElement('img');
                img.src = banner.image_path;
                img.alt = banner.alt_text || 'Advertisement';
                img.style.maxHeight = '100%';
                img.style.maxWidth = '100%';
                img.style.height = 'auto';
                img.style.width = 'auto';
                img.style.objectFit = 'contain';
                img.onerror = () => {
                    // Fallback to text if image fails
                    this.showTextBanner(container, banner.text || 'Advertisement');
                };
                container.appendChild(img);
            } else if (banner.type === 'text' && banner.text && banner.text.trim() !== '') {
                this.showTextBanner(container, banner.text);
            } else {
                // Fallback for empty banners - show logo
                const img = document.createElement('img');
                img.src = this.siteLogo;
                img.alt = 'Rowan Elite Rides';
                img.style.maxHeight = '100%';
                img.style.maxWidth = '100%';
                img.style.height = 'auto';
                img.style.width = 'auto';
                img.style.objectFit = 'contain';
                container.appendChild(img);
            }
            
            // Fade back in
            setTimeout(() => {
                container.style.opacity = '1';
            }, 50);
        }, 150); // Half of transition time
    }

    showTextBanner(container, text) {
        // Create text banner with transparent background and white text with shadow for visibility
        container.innerHTML = `<div style="
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: rgba(0, 0, 0, 0.7) !important;
            color: white !important;
            font-size: 18px !important;
            font-weight: bold !important;
            text-align: center !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
            z-index: 999 !important;
        ">${text}</div>`;

        // Force reflow
        container.offsetHeight;
    }

    // Method to refresh banners (called from admin when settings change)
    async refresh() {
        this.stopRotation();
        await this.loadBanners();

        if (this.isDebugMode()) {
            console.log('Banner system refreshed');
        }
    }


}

// Global banner instance - initialize immediately
try {
    window.cameraBanner = new CameraBanner();
    console.log('CameraBanner loaded - version:', window.cameraBanner.version);
} catch (error) {
    console.error('Failed to create CameraBanner instance:', error);
}

// Initialize the banner system
function initializeBannerSystem() {
    if (window.cameraBanner) {
        console.log('CameraBanner: Starting initialization...');
        window.cameraBanner.loadBanners().then(() => {
            if (window.cameraBanner.isDebugMode()) {
                console.log('CameraBanner: System initialized with', window.cameraBanner.banners.length, 'banners');
            }
        }).catch(error => {
            console.error('CameraBanner: Initialization failed:', error);
        });
    } else {
        console.error('CameraBanner: Instance not found during initialization');
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeBannerSystem);
} else {
    // DOM is already loaded, initialize immediately
    initializeBannerSystem();
}

// Also initialize after a short delay as fallback
setTimeout(() => {
    if (window.cameraBanner && (!window.cameraBanner.banners || window.cameraBanner.banners.length === 0)) {
        console.log('CameraBanner: Fallback initialization...');
        initializeBannerSystem();
    }
}, 1000);

// Additional aggressive initialization for mobile
setTimeout(() => {
    if (window.cameraBanner && (!window.cameraBanner.banners || window.cameraBanner.banners.length === 0)) {
        console.log('CameraBanner: Mobile fallback initialization...');
        window.cameraBanner.loadBanners().catch(error => {
            console.error('CameraBanner: Mobile init failed:', error);
        });
    }
}, 3000);

// Expose a manual trigger for mobile debugging
window.forceBannerInit = function() {
    if (window.cameraBanner) {
        console.log('CameraBanner: Manual initialization triggered');
        return window.cameraBanner.loadBanners().then(() => {
            console.log('CameraBanner: Manual init complete, banners:', window.cameraBanner.banners.length);
            return window.cameraBanner.banners.length;
        });
    } else {
        console.log('CameraBanner: Creating new instance...');
        window.cameraBanner = new CameraBanner();
        return window.cameraBanner.loadBanners().then(() => {
            console.log('CameraBanner: New instance init complete, banners:', window.cameraBanner.banners.length);
            return window.cameraBanner.banners.length;
        });
    }
};

// Banner rotation is handled directly by PWA features when modals are shown/hidden