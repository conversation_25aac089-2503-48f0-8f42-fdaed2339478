{"zencoder.enableRepoIndexing": true, "zencoder.inlineSuggestions.enable": true, "zencoder.codeCompletion.enable": true, "zencoder.codeCompletion.enableMultiLine": true, "dbcode.connections": [{"connectionId": "I6n3Bw9JAsUQblm-kVBRM", "name": "ny-mysql01.offloadsql.com", "driver": "ma<PERSON>b", "connectionType": "host", "host": "ny-mysql01.offloadsql.com", "port": 3306, "ssl": false, "username": "sql24006_forward-limpet", "password": "", "savePassword": "secretStorage", "database": "sql24006_events", "readOnly": false, "role": "production", "connectionTimeout": 30, "driverOptions": {"retrievePublickey": true}}]}