# Show Listing Enhancement

## Overview
This enhancement improves the show listing page with a more intuitive card-based design, advanced filtering capabilities, and pagination.

## Changes Made

### Visual Improvements
- Redesigned show cards with a modern and visually appealing layout
- Enhanced visual indicators with rounded pill badges and icons for show status
- Improved date and time display with separated date and time sections for better readability
- Added registration fee information with badge styling
- Responsive design that works well on all device sizes
- Consistent spacing and typography for better visual hierarchy

### Functional Improvements
- Added search functionality for finding shows by name or description
- Added filtering capabilities:
  - Filter by state (extracted from location field) with improved display format (e.g., "NY - New York")
  - Filter by city (extracted from location field) with dynamic filtering based on selected state
  - Filter by show date
  - Filter by fan voting status
- Added pagination with options to display 20, 50, or 100 shows per page
- Added clear filters button to reset all search parameters
- Added dynamic city dropdown that updates when state selection changes

### Technical Improvements
- Enhanced ShowController with filtering and pagination logic
- Added new methods to ShowModel for filtered queries
- Added location parsing to extract cities and states from location field
- Added debug mode support with detailed logging
- Improved state display with both abbreviation and full name (e.g., "NY - New York")
- Added dynamic city filtering based on selected state
- Added JavaScript event handling for state/city dropdown interaction

## Files Modified
- `/views/show/index.php` - Complete redesign of the show listing page
- `/controllers/ShowController.php` - Added filtering and pagination functionality
- `/models/ShowModel.php` - Added methods for filtered queries and location data extraction

## Files Deleted
- `/controllers/ShowModel.php` - Removed duplicate model file from incorrect location

## Location Parsing
The system now intelligently parses location strings to extract city and state information using:
- Pattern recognition for common formats like "City, State" and "City State"
- State abbreviation and full name mapping
- Case-insensitive matching

## Backup
Original files have been backed up to `/autobackup/show_listing_enhancement/`