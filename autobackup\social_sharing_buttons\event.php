<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Details</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Calendar
                </a>
                <?php if (($data['event']->created_by == $_SESSION['user_id'] || isAdmin()) && empty($data['event']->show_id)): ?>
                <a href="<?php echo URLROOT; ?>/calendar/editEvent/<?php echo $data['event']->id; ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i> Edit Event
                </a>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteEventModal">
                    <i class="fas fa-trash-alt me-2"></i> Delete
                </button>
                <?php elseif (($data['event']->created_by == $_SESSION['user_id'] || isAdmin()) && !empty($data['event']->show_id)): ?>
                <!-- Edit button hidden for show events - edit the show instead -->
                <a href="<?php echo URLROOT; ?>/admin/editShow/<?php echo $data['event']->show_id; ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i> Edit Show
                </a>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteEventModal">
                    <i class="fas fa-trash-alt me-2"></i> Delete
                </button>
                <span class="ms-2 text-muted"><small><i class="fas fa-info-circle"></i> This event is linked to a show. Edit the show to update this event.</small></span>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <?php flash('calendar_message'); ?>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo $data['event']->title; ?></h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-2">
                            <div class="calendar-color-dot me-2" style="background-color: <?php echo $data['event']->color ?: $data['event']->calendar_color ?: '#3788d8'; ?>"></div>
                            <h6 class="mb-0"><?php echo $data['event']->calendar_name; ?></h6>
                        </div>
                        
                        <div class="mb-3">
                            <i class="far fa-calendar-alt me-2"></i>
                            <?php
                            // Format date and time
                            $startDate = new DateTime($data['event']->start_date);
                            $endDate = new DateTime($data['event']->end_date);
                            
                            $dateFormat = 'l, F j, Y';
                            $timeFormat = 'g:i A';
                            
                            if ($data['event']->all_day) {
                                echo $startDate->format($dateFormat);
                                
                                // Check if multi-day event
                                if ($startDate->format('Y-m-d') != $endDate->format('Y-m-d')) {
                                    echo ' to ' . $endDate->format($dateFormat);
                                }
                                
                                echo ' (All day)';
                            } else {
                                echo $startDate->format($dateFormat) . ' at ' . $startDate->format($timeFormat);
                                
                                // Check if same day
                                if ($startDate->format('Y-m-d') == $endDate->format('Y-m-d')) {
                                    echo ' - ' . $endDate->format($timeFormat);
                                } else {
                                    echo ' to ' . $endDate->format($dateFormat) . ' at ' . $endDate->format($timeFormat);
                                }
                            }
                            ?>
                        </div>
                        
                        <?php if (!empty($data['event']->location)): ?>
                        <div class="mb-3">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <?php echo $data['event']->location; ?>
                            
                            <?php if (!empty($data['venue'])): ?>
                            <div class="ms-4 mt-2">
                                <?php if (!empty($data['venue']->address)): ?>
                                <div><?php echo $data['venue']->address; ?></div>
                                <?php endif; ?>
                                
                                <?php if (!empty($data['venue']->city) || !empty($data['venue']->state) || !empty($data['venue']->zip)): ?>
                                <div>
                                    <?php echo $data['venue']->city; ?>
                                    <?php if (!empty($data['venue']->city) && !empty($data['venue']->state)): ?>, <?php endif; ?>
                                    <?php echo $data['venue']->state; ?>
                                    <?php if (!empty($data['venue']->zip)): ?> <?php echo $data['venue']->zip; ?><?php endif; ?>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($data['venue']->latitude) && !empty($data['venue']->longitude)): ?>
                                <div class="mt-2">
                                    <a href="https://maps.google.com/?q=<?php echo $data['venue']->latitude; ?>,<?php echo $data['venue']->longitude; ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-map me-1"></i> View on Map
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['event']->url)): ?>
                        <div class="mb-3">
                            <i class="fas fa-link me-2"></i>
                            <a href="<?php echo $data['event']->url; ?>" target="_blank"><?php echo $data['event']->url; ?></a>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['event']->show_id)): ?>
                        <div class="mb-3">
                            <i class="fas fa-car me-2"></i>
                            <a href="<?php echo URLROOT; ?>/show/view/<?php echo $data['event']->show_id; ?>"><?php echo $data['event']->show_name; ?></a>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['event']->clubs)): ?>
                        <div class="mb-3">
                            <i class="fas fa-users me-2"></i>
                            <strong>Clubs:</strong>
                            <div class="ms-4 mt-1">
                                <?php foreach ($data['event']->clubs as $club): ?>
                                <div class="mb-1"><?php echo $club->name; ?></div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($data['event']->is_recurring): ?>
                        <div class="mb-3">
                            <i class="fas fa-sync-alt me-2"></i>
                            <strong>Recurring Event:</strong>
                            <div class="ms-4 mt-1">
                                <?php echo $data['event']->recurrence_pattern; ?>
                                <?php if (!empty($data['event']->recurrence_end_date)): ?>
                                <div>Until <?php echo date('F j, Y', strtotime($data['event']->recurrence_end_date)); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mb-3">
                            <i class="fas fa-lock me-2"></i>
                            <strong>Privacy:</strong>
                            <?php
                            switch ($data['event']->privacy) {
                                case 'public':
                                    echo 'Public - Visible to everyone';
                                    break;
                                case 'private':
                                    echo 'Private - Visible only to you';
                                    break;
                                case 'members':
                                    echo 'Members - Visible to registered users';
                                    break;
                                default:
                                    echo 'Unknown';
                            }
                            ?>
                        </div>
                    </div>
                    
                    <?php if (!empty($data['event']->description)): ?>
                    <div class="mb-3">
                        <h5>Description</h5>
                        <div class="event-description">
                            <?php echo nl2br(htmlspecialchars($data['event']->description)); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mt-4">
                        <div class="d-flex justify-content-between">
                            <div>
                                <small class="text-muted">Created by: <?php echo $data['event']->created_by == $_SESSION['user_id'] ? 'You' : 'Another user'; ?></small>
                            </div>
                            <div>
                                <a href="<?php echo URLROOT; ?>/calendar/export/<?php echo $data['event']->calendar_id; ?>" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-download me-1"></i> Export Calendar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <?php if (!empty($data['show'])): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Show Details</h5>
                </div>
                <div class="card-body">
                    <h6><?php echo $data['show']->name; ?></h6>
                    
                    <?php if (!empty($data['show']->description)): ?>
                    <div class="mb-3">
                        <?php echo substr($data['show']->description, 0, 150); ?>
                        <?php if (strlen($data['show']->description) > 150): ?>...<?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <strong>Status:</strong> 
                        <span class="badge bg-<?php
                        switch ($data['show']->status) {
                            case 'published':
                                echo 'success';
                                break;
                            case 'draft':
                                echo 'secondary';
                                break;
                            case 'cancelled':
                                echo 'danger';
                                break;
                            case 'completed':
                                echo 'info';
                                break;
                            default:
                                echo 'primary';
                        }
                        ?>">
                            <?php echo ucfirst($data['show']->status); ?>
                        </span>
                    </div>
                    
                    <a href="<?php echo URLROOT; ?>/show/view/<?php echo $data['show']->id; ?>" class="btn btn-primary">
                        View Show Details
                    </a>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($data['venue'])): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Venue Information</h5>
                </div>
                <div class="card-body">
                    <h6><?php echo $data['venue']->name; ?></h6>
                    
                    <?php if (!empty($data['venue']->address) || !empty($data['venue']->city) || !empty($data['venue']->state)): ?>
                    <div class="mb-3">
                        <?php if (!empty($data['venue']->address)): ?>
                        <div><?php echo $data['venue']->address; ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['venue']->city) || !empty($data['venue']->state) || !empty($data['venue']->zip)): ?>
                        <div>
                            <?php echo $data['venue']->city; ?>
                            <?php if (!empty($data['venue']->city) && !empty($data['venue']->state)): ?>, <?php endif; ?>
                            <?php echo $data['venue']->state; ?>
                            <?php if (!empty($data['venue']->zip)): ?> <?php echo $data['venue']->zip; ?><?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['venue']->country)): ?>
                        <div><?php echo $data['venue']->country; ?></div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($data['venue']->phone) || !empty($data['venue']->email) || !empty($data['venue']->website)): ?>
                    <div class="mb-3">
                        <?php if (!empty($data['venue']->phone)): ?>
                        <div><i class="fas fa-phone me-2"></i> <?php echo $data['venue']->phone; ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['venue']->email)): ?>
                        <div><i class="fas fa-envelope me-2"></i> <a href="mailto:<?php echo $data['venue']->email; ?>"><?php echo $data['venue']->email; ?></a></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['venue']->website)): ?>
                        <div><i class="fas fa-globe me-2"></i> <a href="<?php echo $data['venue']->website; ?>" target="_blank"><?php echo $data['venue']->website; ?></a></div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($data['venue']->capacity)): ?>
                    <div class="mb-3">
                        <i class="fas fa-users me-2"></i> Capacity: <?php echo number_format($data['venue']->capacity); ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($data['venue']->notes)): ?>
                    <div class="mb-3">
                        <strong>Notes:</strong>
                        <div><?php echo nl2br(htmlspecialchars($data['venue']->notes)); ?></div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($data['venue']->latitude) && !empty($data['venue']->longitude)): ?>
                    <div class="mb-3">
                        <a href="https://maps.google.com/?q=<?php echo $data['venue']->latitude; ?>,<?php echo $data['venue']->longitude; ?>" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-map-marker-alt me-1"></i> View on Map
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Related Events</h5>
                </div>
                <div class="card-body p-0">
                    <div id="related-events" class="list-group list-group-flush">
                        <!-- Related events will be loaded here via JavaScript -->
                        <div class="list-group-item text-center">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Loading events...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Event Modal -->
<?php if ($data['event']->created_by == $_SESSION['user_id'] || isAdmin()): ?>
<div class="modal fade" id="deleteEventModal" tabindex="-1" aria-labelledby="deleteEventModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEventModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this event?</p>
                <p><strong><?php echo $data['event']->title; ?></strong></p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?php echo URLROOT; ?>/calendar/deleteEvent/<?php echo $data['event']->id; ?>" method="post">
                    <?php echo csrfTokenField(); ?>
                    <button type="submit" class="btn btn-danger">Delete Event</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Custom CSS -->
<style>
    .calendar-color-dot {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-radius: 50%;
    }
    
    .event-description {
        white-space: pre-line;
    }
    
    .related-event {
        border-left: 4px solid #3788d8;
    }
    
    .related-event-title {
        font-weight: 600;
    }
    
    .related-event-time {
        font-size: 0.85rem;
    }
</style>

<!-- JavaScript for related events -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Load related events
        loadRelatedEvents();
        
        function loadRelatedEvents() {
            const relatedEventsContainer = document.getElementById('related-events');
            
            // Get current event details
            const eventId = <?php echo $data['event']->id; ?>;
            const calendarId = <?php echo $data['event']->calendar_id; ?>;
            const eventDate = new Date('<?php echo $data['event']->start_date; ?>');
            
            // Calculate date range (1 month before and after)
            const startDate = new Date(eventDate);
            startDate.setMonth(startDate.getMonth() - 1);
            
            const endDate = new Date(eventDate);
            endDate.setMonth(endDate.getMonth() + 1);
            
            // Fetch related events
            fetch(`<?php echo URLROOT; ?>/calendar/getEvents?calendar_id=${calendarId}&start=${startDate.toISOString()}&end=${endDate.toISOString()}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(events => {
                // Filter out current event and sort by date
                const relatedEvents = events
                    .filter(event => event.id != eventId)
                    .sort((a, b) => new Date(a.start) - new Date(b.start));
                
                // Limit to 5 events
                const displayEvents = relatedEvents.slice(0, 5);
                
                // Clear container
                relatedEventsContainer.innerHTML = '';
                
                if (displayEvents.length === 0) {
                    relatedEventsContainer.innerHTML = '<div class="list-group-item text-center">No related events found</div>';
                    return;
                }
                
                // Add events to container
                displayEvents.forEach(event => {
                    const startDate = new Date(event.start);
                    
                    const dateOptions = { month: 'short', day: 'numeric' };
                    const timeOptions = { hour: 'numeric', minute: '2-digit' };
                    
                    let dateDisplay = startDate.toLocaleDateString(undefined, dateOptions);
                    let timeDisplay = event.allDay ? 'All day' : startDate.toLocaleTimeString(undefined, timeOptions);
                    
                    const eventItem = document.createElement('a');
                    eventItem.href = '<?php echo URLROOT; ?>/calendar/event/' + event.id;
                    eventItem.className = 'list-group-item list-group-item-action related-event';
                    eventItem.style.borderLeftColor = event.backgroundColor || event.borderColor || '#3788d8';
                    
                    eventItem.innerHTML = `
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1 related-event-title">${event.title}</h6>
                            <small>${dateDisplay}</small>
                        </div>
                        <div class="d-flex w-100 justify-content-between">
                            <small class="text-muted">${event.extendedProps.location || ''}</small>
                            <small class="related-event-time">${timeDisplay}</small>
                        </div>
                    `;
                    
                    relatedEventsContainer.appendChild(eventItem);
                });
            })
            .catch(error => {
                console.error('Error loading related events:', error);
                relatedEventsContainer.innerHTML = '<div class="list-group-item text-center text-danger">Error loading related events</div>';
            });
        }
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>