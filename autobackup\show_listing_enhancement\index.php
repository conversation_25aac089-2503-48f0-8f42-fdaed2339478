<?php 
// Ensure APPROOT is defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(dirname(dirname(__FILE__))));
}

// Make APPROOT available globally
if (!isset($GLOBALS['APPROOT'])) {
    $GLOBALS['APPROOT'] = APPROOT;
}

require APPROOT . '/views/includes/header.php'; 
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mb-4">Car Shows</h1>
            
            <?php if (empty($data['shows'])): ?>
                <div class="alert alert-info">
                    <p>There are no upcoming shows at this time. Please check back later.</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($data['shows'] as $show): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo $show->name; ?></h5>
                                    <p class="card-text"><?php echo substr($show->description, 0, 150); ?>...</p>
                                    <div class="mb-3">
                                        <strong>Location:</strong> <?php echo $show->location; ?><br>
                                        <strong>Dates:</strong> <?php echo date('F j, Y', strtotime($show->start_date)); ?> - <?php echo date('F j, Y', strtotime($show->end_date)); ?><br>
                                        <strong>Registration:</strong> <?php echo date('F j, Y', strtotime($show->registration_start)); ?> - <?php echo date('F j, Y', strtotime($show->registration_end)); ?>
                                    </div>
                                    <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $show->id; ?>" class="btn btn-primary">View Details</a>
                                </div>
                                <div class="card-footer">
                                    <?php 
                                    $now = new DateTime();
                                    $regStart = new DateTime($show->registration_start);
                                    $regEnd = new DateTime($show->registration_end);
                                    
                                    if ($now >= $regStart && $now <= $regEnd): ?>
                                        <span class="badge bg-success">Registration Open</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Registration Closed</span>
                                    <?php endif; ?>
                                    
                                    <?php if ($show->fan_voting_enabled): ?>
                                        <span class="badge bg-info">Fan Voting Enabled</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>