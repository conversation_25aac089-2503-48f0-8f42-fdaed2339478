# SQL Parameter Binding Fix - Calendar Filters

## Issue Identified
The calendar advanced filters were failing with SQL error:
```
SQLSTATE[HY093]: Invalid parameter number
```

## Root Cause
When using the same parameter name multiple times in a SQL query, PDO treats each occurrence as a separate parameter that needs to be bound individually. 

**Problematic SQL:**
```sql
(LOWER(e.state) = LOWER(:state) OR LOWER(v.state) = LOWER(:state))
```

PDO expected 2 parameters but only 1 was being bound.

## Solution
Changed to use unique parameter names for each usage:

**Fixed SQL:**
```sql
(LOWER(e.state) = LOWER(:state_e) OR LOWER(v.state) = LOWER(:state_v))
```

With corresponding parameter binding:
```php
$params[':state_e'] = $filters['state'];
$params[':state_v'] = $filters['state'];
```

## Files Modified
- `/models/CalendarModel.php` - Fixed parameter binding for state and city filters
- `/controllers/CalendarController.php` - Added debug logging
- `/config/config.php` - Updated version to 3.35.49
- `/CHANGELOG.md` - Documented the fix

## Testing
After this fix, the calendar filters should work properly:
1. Select State: "NC"
2. Select City: "China Grove" 
3. Click Apply
4. Should show the "kenny" event which matches those criteria

Date: 2025-01-27